{"name": "property-rent-sale", "private": true, "version": "0.0.1", "scripts": {"start": "npm run server | vite", "dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "prepare": "node ./src/server/app.js", "server": "node ./src/server/app.js"}, "dependencies": {"axios": "^1.6.2", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.1", "mui": "^0.0.1", "multer": "^1.4.5-lts.1", "nodemon": "^3.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-router-dom": "^6.26.2", "request": "^2.88.2", "socket.io": "^4.7.2", "socket.io-client": "^4.7.2", "sqlite3": "^5.1.6"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "vite": "^5.4.1"}}