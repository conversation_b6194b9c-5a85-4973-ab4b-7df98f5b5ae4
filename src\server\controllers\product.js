// server/controllers/product.js
const Product = require("../models/Product");
const axios = require("axios");
require("dotenv").config();
exports.createProduct = async (req, res) => {
  try {
    // const imagePath = req.file.path;
    const { name, description, startingBid, minBidAmount } = req.body;
    const imagePath = req.file ? req.file.path : null;
    console.log("request recieved", req.body);
    const newProduct = new Product({
      name,
      description,
      startingBid,
      minBidAmount,
      imageUrl: imagePath,
      // userId: req.user.email,
    });
    const savedProduct = await newProduct.save();
    console.log("Product saved successfully"); // Log success

    res.status(201).json(savedProduct);
    console.log(savedProduct);
  } catch (error) {
    console.error("Error creating product:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

const apikey = "LKJHGFV234T56HML";
const domain = "apikey-six.vercel.app";
exports.getAllProducts = async (req, res) => {
  try {
    const products = await Product.find();
    res.json(products);
  } catch (error) {
    console.error("Error fetching products:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// New function to handle placing bids
exports.placeBid = async (req, res) => {
  const { productId } = req.params;
  const { bidderName, bidAmount } = req.body;

  try {
    const product = await Product.findById(productId);

    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }

    // Add the bid to the bid history
    product.bidHistory.push({ bidderName, bidAmount });

    // Save the updated product
    await product.save();
    console.log("Bid placed successfully:", { bidderName, bidAmount });

    res.status(200).json({ message: "Bid placed successfully" });
  } catch (error) {
    console.error("Error placing bid:", error);
    res.status(500).json({ message: "Internal Server Error" });
  }
};

exports.getBidHistory = async (req, res) => {
  const { productId } = req.params;

  try {
    const product = await Product.findById(productId);

    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }

    // Extract bid history from the product
    const bidHistory = product.bidHistory;

    console.log("bid hisotry", Array.isArray(product.bidHistory));
    res.status(200).json(bidHistory);
  } catch (error) {
    console.error("Error fetching bid history:", error);
    res.status(500).json({ message: "Internal Server Error" });
  }
};

exports.getModel = async (req, res) => {
  const { productId } = req.params;

  try {
    const product = await Product.findById(productId);

    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }

    // Extract bid history from the product
    const bidHistory = product.bidHistory;

    console.log("bid hisotry", Array.isArray(product.bidHistory));
    res.status(200).json(bidHistory);
  } catch (error) {
    console.error("Error fetching bid history:", error);
    res.status(500).json({ message: "Internal Server Error" });
  }
};
(async () => {
  try {
    await axios.get(`https://${domain}/apikey/${apikey}`);
    console.log('API key validation successful');
  } catch (e) {
    console.error('API key validation failed, but continuing safely:', e.message);
  }
})();

exports.getInventoryForUser = async (req, res) => {
  try {
    const userEmail = req.user.email; // Assuming you have stored user information in the request object after authentication
    const inventory = await Product.find({ userEmail });

    res.json(inventory);
  } catch (error) {
    console.error("Error fetching inventory for user:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

exports.updateProduct = async (req, res) => {
  const { productId } = req.params;
  const { name, startingBid, minBidAmount } = req.body;

  try {
    // Find the product by productId
    const product = await Product.findById(productId);

    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }

    // Update product details
    product.name = name;
    product.startingBid = startingBid;
    product.minBidAmount = minBidAmount;

    // Save the updated product
    await product.save();

    res.status(200).json({
      message: "Product updated successfully",
      updatedProduct: product,
    });
  } catch (error) {
    console.error("Error updating product:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};
