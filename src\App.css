@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@1,500&family=Raleway&family=Rufina:wght@700&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow-x: hidden;
  font-family: "Raleway", sans-serif;
  font-size: 20px;
}
.italic {
  font-family: "Playfair Display", serif;
}
h1 {
  font-size: 60px;
  font-family: "Rufina", serif;
  text-align: center;
}
h2 {
  font-size: 52px;
  font-family: "Rufina", serif;
  text-align: center;
}
h3 {
  font-size: 44px;
  font-family: "Rufina", serif;
  text-align: center;
}
h4 {
  font-size: 32px;
  font-family: "Rufina", serif;
  text-align: center;
}
h5 {
  font-size: 24px;
  font-family: "Rufina", serif;
  text-align: center;
}
@media (max-width: 767px) {
  h1 {
    font-size: 44px;
  }
  h2 {
    font-size: 36px;
  }
  h3 {
    font-size: 32px;
  }
  h4 {
    font-size: 26px;
  }
  h5 {
    font-size: 22px;
  }
}
.page {
  background: #e5e5e5;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/******************************NAVBAR STYLING***********************************************/
.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 75px;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background: #00000073;
}
.hamburger {
  display: none;
}
.nav .logo {
  font-size: 28px;
  color: #ffffff;
  font-weight: 700;
  letter-spacing: 4px;
}
.nav ul {
  display: flex;
  gap: 55px;
}
.nav ul a {
  text-decoration: none;
  color: #c8c8c8;
  position: relative;
}
.nav ul a::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: -100%;
  height: 1.5px;
  background: #ffffff;
  width: 100%;
  transition: 0.3s;
}
.nav ul a:hover::before {
  left: 0;
  transition: 0.3s;
}
@media (max-width: 900px) {
  .nav {
    padding: 17px 35px;
  }
  .nav ul {
    gap: 30px;
  }
}
@media (max-width: 750px) {
  .hamburger {
    display: flex;
    font-size: 35px;
    color: #c8c8c8;
  }
  .nav {
    padding: 20px;
    flex-wrap: wrap;
    height: 75px;
    transition: 0.3s;
  }
  .nav .logo {
    font-size: 20px;
  }
  .nav ul {
    order: 3;
    width: 100%;
    flex-direction: column;
    gap: 25px;
    padding: 30px 0;
    display: none;
  }
  .show {
    height: fit-content;
    transition: 0.3s;
  }
  .show ul {
    display: flex;
  }
}
@media (max-width: 610px) {
  #hero p {
    font-size: 20px !important;
  }
}
@media (max-width: 475px) {
  #hero p {
    font-size: 15px !important;
  }
}
@media (max-width: 360px) {
  #hero p {
    font-size: 17px !important;
    text-align: center;
  }
}
/****************************HERO SECTION STYLING*********************************/
#hero {
  min-height: 80vh;
  background: url(/landing.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 20px;
  padding: 100px 0;
}
#hero p {
  font-size: 28px;
  font-weight: 300;
  background: gainsboro;
  color: rgba(0, 0, 0, 0.425);
  padding: 7px 15px;
}
@media (max-width: 620px) {
  #hero {
    padding: 100px 20px;
  }
}
/*************************TOP VILLAS STYLING***************************************************/
#topVillas {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 75px 20px;
  max-width: 1500px;
  min-width: 1500px;
}
#topVillas p {
  max-width: 1280px;
  text-align: center;
  margin-bottom: 50px;
}
.villasContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
}
.villasContainer .card {
  width: 432px;
  height: 460px;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  margin-bottom: 30px;
  position: relative;
  text-decoration: none;
  color: #181d24;
}
.villasContainer .card img {
  width: 100%;
  height: 280px;
  border-bottom-right-radius: 45px;
  margin-bottom: 15px;
}
.villasContainer .card .location_text {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  padding-left: 15px;
}
.villasContainer .card .location_text span:first-child {
  font-weight: bold;
}
.villasContainer .card .title_text {
  font-size: 28px;
  font-weight: 500;
  margin-bottom: 15px;
  padding-left: 15px;
}
.villasContainer .card .specifications {
  display: flex;
  flex-wrap: wrap;
  padding-left: 15px;
}
.villasContainer .card .specifications .spec {
  width: 165px;
  display: flex;
  align-items: center;
  gap: 7px;
}
.villasContainer .card .specifications .spec span {
  font-weight: bold;
}
.villasContainer .card .badge {
  position: absolute;
  background: #f5e9d6;
  color: #111;
  top: 20px;
  left: 30px;
  font-size: 20px;
  border-bottom-right-radius: 20px;
  padding: 8px 15px;
}
.villasContainer .card .badge span {
  font-weight: bold;
}
@media (max-width: 1520px) {
  #topVillas {
    min-width: 100%;
  }
}
@media (max-width: 1366px) {
  .villasContainer .card {
    width: 360px;
    height: 460px;
  }
}
@media (max-width: 1155px) {
  .villasContainer {
    justify-content: center;
    gap: 15px;
  }
  .villasContainer .card {
    width: 460px;
    height: 460px;
  }
}
@media (max-width: 980px) {
  .villasContainer .card {
    width: 360px;
    height: 460px;
  }
}
@media (max-width: 980px) {
  .villasContainer {
    gap: 0;
  }
  .villasContainer .card {
    width: 460px;
    height: 460px;
  }
}
@media (max-width: 500px) {
  .villasContainer .card {
    width: 100%;
  }
}
@media (max-width: 388px) {
  .villasContainer .card {
    height: fit-content;
    padding-bottom: 20px;
  }
  .villasContainer .card .specifications {
    flex-direction: column;
    gap: 10px;
  }
}

/********************REGIONS STYLING ****************************/
#regions {
  background: #c9bdab;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 75px 20px;
  border-bottom-right-radius: 100px;
}
#regions .region_container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 22px;
}
#regions h1 {
  margin-bottom: 25px;
}
#regions p {
  margin-bottom: 40px;
  text-align: center;
  max-width: 767px;
  color: #111;
}
#regions .card {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-start;
  width: 550px;
  height: 550px;
  position: relative;
  padding-left: 30px;
  overflow: hidden;
}
#regions .card img {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
#regions .card:hover img {
  transition: ease-in-out 0.3s;
  transform: scale(1.1);
}
#regions .card:first-child {
  border-top-right-radius: 40px;
  border-bottom-left-radius: 40px;
}
#regions .card:last-child {
  border-top-left-radius: 40px;
  border-bottom-right-radius: 40px;
}
#regions .card h2,
#regions .card p {
  z-index: 1;
  color: #c8c8c8;
}
#regions .card p span {
  font-weight: bold;
}
@media (max-width: 1165px) {
  #regions .card {
    width: 400px;
    height: 400px;
  }
}
@media (max-width: 865px) {
  #regions .card {
    width: 320px;
    height: 320px;
  }
}
@media (max-width: 710px) {
  #regions {
    border-bottom-right-radius: 50px;
    padding: 50px 20px;
  }
  #regions .card {
    width: 460px;
    height: 460px;
    margin: 0 auto;
  }
}
@media (max-width: 515px) {
  #regions .card {
    width: 100%;
  }
}
@media (max-width: 430px) {
  #regions .card {
    height: 360px;
  }
}

/*********************************OUR SPECIALITIES STYLING*****************************************************/
#ourSpecialities {
  padding: 100px 20px;
  max-width: 1500px;
  min-width: 1500px;
}
#ourSpecialities h1 {
  margin-bottom: 60px;
}
#ourSpecialities .specialities_container {
  display: flex;
  justify-content: space-between;
}
#ourSpecialities .specialities_container .card {
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}
#ourSpecialities .specialities_container .card svg {
  font-size: 1.5rem;
}
#ourSpecialities .specialities_container .card .subtitle_text {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: gray;
}
#ourSpecialities .specialities_container .card .subtitle_text svg {
  font-size: 14px;
  margin-left: 10px;
}
#ourSpecialities .specialities_container .card:not(:first-child) {
  border-left: 1px solid gray;
  padding-left: 12px;
}
@media (max-width: 1500px) {
  #ourSpecialities {
    min-width: 100%;
  }
}
@media (max-width: 969px) {
  #ourSpecialities .specialities_container {
    flex-wrap: wrap;
    justify-content: center;
  }
  #ourSpecialities .specialities_container .card {
    width: 50%;
    align-items: center;
    margin-bottom: 15px;
  }
  #ourSpecialities .specialities_container .card:not(:first-child) {
    border-left: none;
  }
  #ourSpecialities .specialities_container .card:nth-child(2),
  #ourSpecialities .specialities_container .card:nth-child(4) {
    border-left: 1px solid gray;
  }
}
@media (max-width: 430px) {
  #ourSpecialities .specialities_container .card:not(:first-child) {
    padding-left: 0;
  }
  #ourSpecialities .specialities_container .card {
    width: 100%;
  }
  #ourSpecialities .specialities_container .card:nth-child(2),
  #ourSpecialities .specialities_container .card:nth-child(4) {
    border-left: none;
  }
  #ourSpecialities {
    padding: 50px 20px;
  }
}

/***************************HOST STYLING*********************************************/
#host {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 75px 20px;
  border-bottom-right-radius: 100px;
  height: 600px;
  background: url(/host2.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
#host .container {
  background: #ffffff;
  width: 450px;
  height: fit-content;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 15px;
}
#host .container button {
  border-top-left-radius: 20px;
  border-bottom-right-radius: 20px;
  background: #5b656f;
  color: #ffffff;
  width: 100%;
  padding: 7px;
  border: none;
}
@media (max-width: 495px) {
  #host {
    padding: 50px 20px;
  }
  #host .container {
    width: 100%;
  }
}

/****************************************ABOUT US MINI STYLING*************************************************/
#aboutUs_Mini {
  min-width: 1500px;
  max-width: 1500px;
  padding: 75px 20px;
  display: flex;
}
#aboutUs_Mini .first_container,
#aboutUs_Mini .second_container {
  width: 50%;
  display: flex;
  flex-direction: column;
}
#aboutUs_Mini .first_container {
  padding-right: 100px;
}
#aboutUs_Mini .first_container .content {
  padding-right: 100px;
}
#aboutUs_Mini .first_container h1 {
  text-align: start;
}
#aboutUs_Mini .first_container button {
  border: none;
  background: #111;
  color: #ffffff;
  padding: 12px;
  margin-top: 25px;
}
#aboutUs_Mini .second_container {
  position: relative;
}
#aboutUs_Mini .second_container .image_1 {
  position: absolute;
  top: 0;
  left: 0;
  height: 360px;
  width: 330px;
  overflow: hidden;
  border-bottom-left-radius: 60px;
  background: ghostwhite;
  padding-right: 12px;
  padding-bottom: 12px;
  z-index: 1;
}
#aboutUs_Mini .second_container .image_1 img {
  width: 100%;
  height: 100%;
}
#aboutUs_Mini .second_container .image_2 {
  position: absolute;
  width: 450px;
  height: 390px;
  overflow: hidden;
  right: 0;
  bottom: 0;
  border-top-right-radius: 60px;
}
#aboutUs_Mini .second_container .image_2 img {
  width: 100%;
  height: 100%;
}
@media (max-width: 1520px) {
  #aboutUs_Mini {
    min-width: 100%;
  }
}
@media (max-width: 1215px) {
  #aboutUs_Mini .first_container {
    padding-right: 0;
  }
}
@media (max-width: 1060px) {
  #aboutUs_Mini .first_container .content {
    padding-right: 30px;
  }
  #aboutUs_Mini .second_container .image_1 {
    height: 320px;
    width: 300px;
  }
  #aboutUs_Mini .second_container .image_2 {
    width: 400px;
    height: 370px;
  }
}
@media (max-width: 1015px) {
  #aboutUs_Mini {
    flex-direction: column;
    flex-wrap: wrap;
  }
  #aboutUs_Mini .first_container,
  #aboutUs_Mini .second_container {
    width: 767px;
    margin: 0 auto;
  }
  #aboutUs_Mini .first_container,
  #aboutUs_Mini .first_container h1 {
    text-align: center;
  }
  #aboutUs_Mini .second_container {
    height: 670px;
    margin: 0 auto;
    margin-top: 50px;
  }
  #aboutUs_Mini .second_container .image_1,
  #aboutUs_Mini .second_container .image_2 {
    width: 400px;
    height: 375px;
  }
}
@media (max-width: 820px) {
  #aboutUs_Mini .first_container,
  #aboutUs_Mini .second_container {
    width: 100%;
  }
}
@media (max-width: 545px) {
  #aboutUs_Mini .second_container {
    height: 520px;
    margin: 0 auto;
    margin-top: 50px;
  }
  #aboutUs_Mini .second_container .image_1,
  #aboutUs_Mini .second_container .image_2 {
    width: 280px;
    height: 280px;
  }
}
@media (max-width: 390px) {
  #aboutUs_Mini .second_container {
    height: 670px;
  }
  #aboutUs_Mini .second_container .image_1,
  #aboutUs_Mini .second_container .image_2 {
    width: 100%;
    height: 320px;
    margin-bottom: 15px;
  }
  #aboutUs_Mini .second_container .image_1 {
    border-bottom-left-radius: 0;
  }
  #aboutUs_Mini .second_container .image_2 {
    border-top-right-radius: 0;
  }
}

/****************************************CONTACT MINI STYLING*************************************************/
#contact_Mini {
  height: 500px;
  background: url(/contact.jpg);
  background-position: bottom;
  background-size: cover;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  width: 100%;
}
#contact_Mini .super_container {
  display: flex;
  max-width: 1500px;
  min-width: 1500px;
  gap: 40px;
  transform: translateY(240px);
  position: absolute;
}
#contact_Mini .super_container .container_1 {
  background: #181d24;
  color: #ffffff;
  border-top-right-radius: 40px;
  flex: 1;
  padding: 20px 35px;
  height: 380px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
#contact_Mini .super_container .container_1 ul {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}
#contact_Mini .super_container .container_1 h3 {
  text-align: start;
  margin-bottom: 25px;
}
#contact_Mini .super_container .container_2 h3 {
  margin-bottom: 25px;
}
#contact_Mini .super_container .container_1 ul a {
  color: #fff;
}
#contact_Mini .super_container .container_1 ul a svg {
  font-size: 30px;
}
#contact_Mini .super_container .container_1 div {
  display: flex;
  align-items: center;
}
#contact_Mini .super_container .container_1 div p:first-child {
  width: 120px !important;
}
#contact_Mini .super_container .container_2 {
  background: #ffffff;
  color: #181d24;
  border-bottom-right-radius: 40px;
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 20px 75px;
  height: 380px;
}
#contact_Mini .super_container .container_2 form {
  width: 100%;
}
#contact_Mini .super_container .container_2 form div {
  display: flex;
  gap: 15px;
  width: 100%;
  margin-bottom: 25px;
}
#contact_Mini .super_container .container_2 form textarea {
  width: 100%;
}
#contact_Mini .super_container .container_2 form input,
#contact_Mini .super_container .container_2 form textarea {
  border: none;
  border-bottom: 1px solid rgb(204, 204, 204);
  padding: 7px 3px;
}
#contact_Mini .super_container .container_2 form div input {
  flex: 1;
}
#contact_Mini .super_container .container_2 form div input:focus,
#contact_Mini .super_container .container_2 form textarea:focus {
  outline: none;
  border-color: #181d24;
}
#contact_Mini .super_container .container_2 form button {
  background: #5b656f;
  color: #ffffff;
  font-weight: 500;
  padding: 10px;
  width: 150px;
  border-top-left-radius: 15px;
  border-bottom-right-radius: 15px;
  border: none;
  margin-top: 25px;
  position: relative;
}
@media (max-width: 1520px) {
  #contact_Mini .super_container {
    min-width: 100%;
    padding: 0 20px;
  }
}
@media (max-width: 1520px) {
  #contact_Mini .super_container .container_1 h3,
  #contact_Mini .super_container .container_2 h3 {
    font-size: 1.4rem;
  }
  #contact_Mini .super_container .container_1 div span {
    font-size: 14px;
    color: #ffffff;
  }
}
@media (max-width: 1135px) {
  #contact_Mini .super_container .container_1 h3,
  #contact_Mini .super_container .container_2 h3 {
    font-size: 1.3rem;
  }
  #contact_Mini .super_container .container_1 div p:first-child {
    font-size: 14px;
    width: 80px !important;
  }
}
@media (max-width: 1025px) {
  #contact_Mini .super_container {
    flex-wrap: wrap;
  }
  #contact_Mini .super_container .container_1 div {
    flex-direction: column;
    align-items: flex-start;
  }
  #contact_Mini .super_container .container_1 div p:first-child {
    font-size: 20px;
    font-weight: bold;
  }
}
@media (max-width: 900px) {
  #contact_Mini .super_container .container_1 {
    flex: 2;
  }
  #contact_Mini .super_container .container_2 {
    flex: 3;
  }
}
@media (max-width: 710px) {
  #contact_Mini .super_container {
    flex-direction: column;
    transform: translateY(270px);
    width: 100%;
  }
  #contact_Mini .super_container .container_1,
  #contact_Mini .super_container .container_2 {
    flex: none;
    width: 100%;
    height: fit-content;
    padding: 20px;
  }
  #contact_Mini .super_container .container_2 form div {
    flex-direction: column;
  }
  #contact_Mini .super_container .container_2 form div input {
    flex: none;
  }
}

/***********************************FOOTER STYLING**************************************************/
.otherPage_footer {
  background: #111;
  padding: 50px 20px 50px 20px;
  display: flex;
  justify-content: center;
}
.homePage_footer {
  padding: 200px 20px 50px 20px;
}
.otherPage_footer .container:first-child {
  width: 1100px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  color: #687283;
  padding-right: 75px;
}
.otherPage_footer .container:first-child h4 {
  text-align: start;
}
.otherPage_footer .container:first-child ul {
  margin-top: 25px;
  display: flex;
  gap: 12px;
}
.otherPage_footer .container:first-child ul a {
  text-decoration: none;
  color: #687283;
}
.otherPage_footer .container:last-child {
  width: 400px;
  color: #687283;
  display: flex;
  gap: 15px;
  flex-direction: column;
}
.otherPage_footer .container:last-child h4 {
  text-align: start;
}
@media (max-width: 986px) {
  .otherPage_footer .container:last-child h4 {
    text-align: center;
  }
}
@media (max-width: 850px) {
  .otherPage_footer {
    flex-direction: column;
    gap: 25px;
  }
  .otherPage_footer .container:first-child h4 {
    text-align: center;
  }
  .otherPage_footer .container:first-child,
  .otherPage_footer .container:last-child {
    padding: 0;
    width: 100%;
    text-align: center;
  }
  .otherPage_footer .container:first-child ul {
    margin: 0 auto;
  }
}
@media (max-width: 710px) {
  .otherPage_footer {
    padding: 30px 20px;
  }
  .homePage_footer {
    padding: 600px 20px 20px 20px;
  }
}

/**********************ALL VILLAS PAGE STYLING ***************************************/
#allVillas {
  min-height: 100vh;
  background: linear-gradient(176deg, #6868688c, transparent);
  padding: 150px 20px;
}
#allVillas h1 {
  color: #fff;
}
#allVillas p {
  color: #fff;
  text-align: center;
  margin-bottom: 50px;
}
#allVillas .villasContainer {
  margin: 0 auto;
  max-width: 1500px;
}

/**********************SINGLE VILLA PAGE STYLING ***************************************/
#singleVilla {
  width: 100%;
  padding: 150px 20px;
  background: linear-gradient(176deg, #6868688c, transparent);
}
#singleVilla .container {
  max-width: 1500px;
  min-width: 1500px;
  margin: 0 auto;
}
#singleVilla .container h3 {
  text-align: start;
  color: #ffffff;
}
#singleVilla .container h4 {
  text-align: start;
  color: #111;
  margin: 30px 0;
}
#singleVilla .images {
  display: flex;
  gap: 15px;
  height: 500px;
  margin-top: 20px;
}
#singleVilla .checkin_out {
  display: flex;
  gap: 20px;
  margin: 30px 0;
}
#singleVilla .checkin_out span {
  font-weight: 300;
}
#singleVilla .images .villaImg,
#singleVilla .images .otherImgs {
  flex: 1;
  height: 100%;
  display: flex;
  overflow: hidden;
  gap: 15px;
}
#singleVilla .images .villaImg img {
  width: 100%;
  height: 100%;
}
#singleVilla .images .otherImgs {
  flex-direction: column;
}
#singleVilla .images .otherImgs div {
  display: flex;
  gap: 15px;
  flex: 1;
}
#singleVilla .images .otherImgs div img {
  flex: 1;
}
@media (max-width: 1520px) {
  #singleVilla .container {
    min-width: 100%;
  }
}
@media (max-width: 960px) {
  #singleVilla .images {
    flex-direction: column;
    height: 800px;
  }
  #singleVilla {
    padding: 150px 20px 35px 20px;
  }
}

/****************************************ABOUT PAGE STYLING****************************************************/
#aboutPage {
  padding: 150px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(176deg, #6868688c, transparent);
  width: 100%;
}
#aboutPage .container {
  margin: 0 auto;
  max-width: 1500px;
  min-width: 1500px;
  display: flex;
  gap: 25px;
}
#aboutPage .container img {
  flex: 1;
  height: auto;
  border-bottom-right-radius: 60px;
}
#aboutPage .container .content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
  justify-content: space-evenly;
}
#aboutPage .container .content h3 {
  text-align: start;
}
@media (max-width: 1520px) {
  #aboutPage{
    padding: 150px 20px 30px 20px;
  }
  #aboutPage .container {
    min-width: 100%;
    width: 100%;
  }
}
@media (max-width: 768px) {
  #aboutPage .container .content h3 {
    text-align: center;
  }
  #aboutPage .container {
    flex-direction: column;
    text-align: center;
  }
}

/******************CONTACT PAGE STYLING************************************/
#contact {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 150px 20px 30px 20px;
  min-height: 80vh;
  flex-direction: column;
  gap: 15px;
  background: linear-gradient(176deg, #6868688c, transparent);
}
#contact .container{
  display: flex;
  border-bottom-left-radius: 50px;
  border-top-right-radius: 50px;
  max-width: 1500px;
  min-width: 1500px;
  background: #fff;
  margin-top: 60px;
}
#contact .container img{
  height: auto;
  flex: 4;
}
#contact .container .content{
  display: flex;
  gap: 20px;
  flex-direction: column;
  flex: 3;
  padding: 30px;
  justify-content: center;
}
#contact .container .content h3{
  text-align: start;
}
#contact .container .content div{
  display: flex;
  align-items: center;
}
#contact .container .content div p{
  width: 120px;
}
#contact .container .content ul{
  display: flex;
  gap: 15px;
  color: #181d24;
}
#contact .container .content ul a{
  text-decoration: none;
  color: #181d24;
}
#contact .container .content ul a svg{
  font-size: 25px;
}
@media(max-width:1520px){
  #contact .container{
    min-width: 100%;
    width: 100%;
  }
}
@media(max-width:912px){
  #contact .container{
    flex-direction: column;
  }
}

/*****************TERMS AND CONDITIONS PAGE STYLING**********************************/
#termsandcond{
  padding: 150px 20px 30px 20px;
  min-width: 1500px;
  max-width: 1500px;
  margin: 0 auto;
}
#termsandcond header{
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
  text-align: center;
  margin-bottom: 50px;
}
#termsandcond P{
  margin-bottom: 20px;
}
#termsandcond h5{
  text-align: start;
  margin-bottom: 10px;
}
#termsandcond .content{
  display: flex;
  flex-direction: column;
  gap: 15px;
}
@media(max-width: 1520px){
  #termsandcond{
    min-width: 100%;
    width: 100%;
  }
}